import { useRef, useEffect, useState, useCallback, memo } from 'react';
import { useVisitorTracking } from '../hooks/useVisitorTracking';
import { logDebug } from '../utils/logger';

// Move portfolio items outside component to prevent recreation on each render
const PORTFOLIO_ITEMS = [
    { // ID : 0
      id: 'portfolio-0-3d-ecommerce',
      href: "https://threed-e-commerce.onrender.com",
      image: "/3D E-Comm.PNG",
      alt: "3D Ecommerce",
      title: "3D Ecommerce",
      forSale: true
    },
    { // ID : 1
      id: 'portfolio-1-will-be-deployed-soon',
      href: "#",
      image: "/ex1.webp",
      alt: "Will be deployed soon.",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // ID : 2
      id: 'portfolio-2-professional-portfolio',
      href: "https://creative-website-jumper.onrender.com",
      image: "/P1.PNG",
      alt: "Nexit Brand Identity",
      title: "Professional Portfolio",
      forSale: true
    },
    { // ID : 3
      id: 'portfolio-3-will-be-deployed-soon',
      href: "#",
      image: "/ex3.webp",
      alt: "Will be deployed soon.s",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // ID : 4
      id: 'portfolio-4-available',
      href: "https://flawless-2pqq.onrender.com",
      image: "/Flaw.PNG",
      alt: "Flaw",
      title: "Available",
      forSale: true
    },
    { // ID : 5
      id: 'portfolio-5-will-be-deployed-soon',
      href: "#",
      image: "/ex5.png",
      alt: "Will be deployed soon.",
      title: "Will be deployed soon.",
      forSale: false
    },
    { // ID : 6
      id: 'portfolio-6-experience-digital-banking-with-ai',
      href: "https://hoobank-neon-future.onrender.com",
      image: "/HooBank.png",
      alt: "Business Web UI",
      title: "Experience digital banking with AI ",
      forSale: true
    }
];

const Portfolio = memo(() => {
  const { ref: portfolioRef } = useVisitorTracking('portfolio', {
    threshold: 0.3,
    minDuration: 4
  });

  // State for notification
  const [showNotification, setShowNotification] = useState(false);

  // Project tracking function
  const trackProjectInteraction = useCallback(async (item, interactionType = 'click', originalIndex = 0) => {
    const API_URL = process.env.REACT_APP_API_URL;
    if (!API_URL) return;

    // Use the item's unique ID, fallback to generating one if not available
    const projectId = item.id || `portfolio-${originalIndex % PORTFOLIO_ITEMS.length}-${item.title.replace(/\s+/g, '-').toLowerCase()}`;
    const isAvailable = item.href !== "#";

    try {
      await fetch(`${API_URL}/api/track/visit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: `portfolio-item-${interactionType}`,
          duration: 1, // Minimal duration for interaction tracking
          sessionId: `${Date.now()}-${Math.random()}`,
          pageUrl: window.location.href,
          projectTitle: item.title,
          projectType: 'portfolio-carousel',
          projectId: projectId,
          projectAvailable: isAvailable,
          projectUrl: isAvailable ? item.href : '',
          interactionType: interactionType
        }),
      });

      logDebug(`📊 Portfolio Project Interaction: ${item.title} - ${interactionType}`, {
        projectId,
        available: isAvailable,
        url: item.href
      });
    } catch (error) {
      console.warn('Portfolio project interaction tracking failed:', error);
    }
  }, []);

  // Refs and state for carousel functionality
  const carouselRef = useRef(null);
  const trackRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Touch event handlers state - declare early
  const [touchStartTime, setTouchStartTime] = useState(0);
  const [hasMoved, setHasMoved] = useState(false);
  const touchTimeoutRef = useRef(null);

  // Use refs to store current state values for the animation loop
  const stateRef = useRef({
    isHovered: false,
    isDragging: false
  });

  // Update state ref whenever state changes
  useEffect(() => {
    stateRef.current = {
      isHovered,
      isDragging
    };
  }, [isHovered, isDragging]);

  // Auto-scroll functionality - NEVER STOP continuous infinite scrolling
  useEffect(() => {
    let animationId;

    const autoScroll = () => {
      const { isHovered: hovering, isDragging: dragging } = stateRef.current;

      if (trackRef.current && !hovering && !dragging) {
        const track = trackRef.current;
        const scrollAmount = 1; // Pixels per frame

        // Get actual scroll width from DOM
        const actualScrollWidth = track.scrollWidth;
        const maxScroll = actualScrollWidth / 2; // Half width (one complete set)

        // Smooth continuous scrolling
        track.scrollLeft += scrollAmount;

        // NEVER STOP - seamless infinite loop when reaching the end
        if (track.scrollLeft >= maxScroll - 10) { // Small buffer to prevent issues
          track.scrollLeft = 0; // Reset to start immediately
        }
      }

      // Continue the animation loop - NEVER STOP
      animationId = requestAnimationFrame(autoScroll);
    };

    // Start the infinite animation loop immediately
    animationId = requestAnimationFrame(autoScroll);

    // Additional safety mechanism for mobile - force resume auto-scroll periodically
    const mobileResumeInterval = setInterval(() => {
      // On mobile, if we've been stuck in dragging state for too long, reset it
      if (window.innerWidth <= 768 && stateRef.current.isDragging) {
        // Simply reset if we've been dragging for too long (don't rely on touchStartTime here)
        setIsDragging(false);
        setHasMoved(false);
      }
    }, 2000); // Check every 2 seconds and reset if still dragging

    // Handle page visibility changes to ensure auto-scroll resumes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible again, ensure auto-scroll can resume
        setIsDragging(false);
        setHasMoved(false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      clearInterval(mobileResumeInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []); // No dependencies - run once and never stop

  // Mouse event handlers for desktop drag functionality
  const handleMouseDown = (e) => {
    if (!trackRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - trackRef.current.offsetLeft);
    setScrollLeft(trackRef.current.scrollLeft);
    trackRef.current.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !trackRef.current) return;
    e.preventDefault();
    const track = trackRef.current;
    const x = e.pageX - track.offsetLeft;
    const walk = (x - startX) * 2; // Multiply for faster scroll
    const newScrollLeft = scrollLeft - walk;
    const maxScroll = track.scrollWidth / 2; // Half of actual scroll width

    track.scrollLeft = newScrollLeft;

    // Handle infinite scroll boundaries for manual drag
    if (track.scrollLeft >= maxScroll) {
      track.scrollLeft = 0; // Reset to start
      setScrollLeft(0); // Update the reference point
      setStartX(x); // Update the start position
    } else if (track.scrollLeft < 0) {
      track.scrollLeft = maxScroll - 1; // Jump to end
      setScrollLeft(maxScroll - 1); // Update the reference point
      setStartX(x); // Update the start position
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    setIsHovered(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  // Touch event handlers for mobile swipe functionality
  const handleTouchStart = useCallback((e) => {
    if (!trackRef.current) return;

    // Clear any existing timeout
    if (touchTimeoutRef.current) {
      clearTimeout(touchTimeoutRef.current);
      touchTimeoutRef.current = null;
    }

    const touch = e.touches[0];
    setStartX(touch.clientX);
    setScrollLeft(trackRef.current.scrollLeft);
    setTouchStartTime(Date.now());
    setHasMoved(false);

    // Don't prevent default immediately - let clicks work
    // Only set dragging state, don't prevent default yet
    setIsDragging(false); // Start as false, will be set to true if user moves

    // Safety timeout to ensure auto-scroll resumes if touch events get stuck
    touchTimeoutRef.current = setTimeout(() => {
      setIsDragging(false);
      setHasMoved(false);
    }, 1000); // Reset after 1 second if no touchend
  }, []);

  const handleTouchMove = useCallback((e) => {
    if (!trackRef.current) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - startX);

    // Only start dragging if user has moved more than 10px
    if (deltaX > 10 && !hasMoved) {
      setIsDragging(true);
      setHasMoved(true);
      // Now prevent default to enable dragging
      e.preventDefault();
    }

    // If we're dragging, continue to prevent default and update scroll
    if (isDragging || hasMoved) {
      e.preventDefault(); // Prevent page scrolling
      const walk = (startX - touch.clientX) * 1.5; // Adjust sensitivity
      trackRef.current.scrollLeft = scrollLeft + walk;
    }
  }, [isDragging, startX, scrollLeft, hasMoved]);

  // Handle touch cancel (when touch is interrupted)
  const handleTouchCancel = useCallback(() => {
    // Clear the safety timeout
    if (touchTimeoutRef.current) {
      clearTimeout(touchTimeoutRef.current);
      touchTimeoutRef.current = null;
    }

    // Reset all touch states to ensure auto-scroll resumes
    setIsDragging(false);
    setHasMoved(false);
  }, []);

  const handleTouchEnd = useCallback((e) => {
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - touchStartTime;

    // Clear the safety timeout
    if (touchTimeoutRef.current) {
      clearTimeout(touchTimeoutRef.current);
      touchTimeoutRef.current = null;
    }

    // Always reset dragging state to ensure auto-scroll can resume
    setIsDragging(false);
    setHasMoved(false);

    // If it was a quick tap (less than 200ms) and no movement, it's a click
    // Don't prevent default to allow the click to work
    if (touchDuration < 200 && !hasMoved) {
      // This is a tap/click - don't prevent default, let it through
      return;
    }

    // If we were dragging, prevent any click events
    if (hasMoved) {
      e.preventDefault();
    }
  }, [hasMoved, touchStartTime]);

  // Wheel event handler for mouse wheel scrolling with infinite scroll support
  const handleWheel = useCallback((e) => {
    if (!trackRef.current) return;

    // Only handle wheel events when hovering over the carousel
    // This prevents page scrolling when interacting with the carousel
    e.preventDefault();
    e.stopPropagation();

    const track = trackRef.current;
    const scrollAmount = e.deltaY * 2; // Multiply for more responsive scrolling
    const maxScroll = track.scrollWidth / 2; // Half of actual scroll width

    track.scrollLeft += scrollAmount;

    // Handle infinite scroll boundaries for manual wheel scrolling
    if (track.scrollLeft >= maxScroll) {
      track.scrollLeft = 0; // Reset to start
    } else if (track.scrollLeft < 0) {
      track.scrollLeft = maxScroll - 1; // Jump to end
    }
  }, []);

  // Hover handlers for auto-scroll pause/resume
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeaveCarousel = () => {
    setIsHovered(false);
  };



  // Handle wheel events with proper event listener options to avoid passive event listener issues
  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    // Add wheel event listener with { passive: false } to allow preventDefault
    carousel.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      carousel.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  // Handle touch events with proper event listener options to avoid passive event listener issues
  useEffect(() => {
    const track = trackRef.current;
    if (!track) return;

    // Add touch event listeners with { passive: false } to allow preventDefault when needed
    track.addEventListener('touchstart', handleTouchStart, { passive: true }); // Passive for touchstart to allow clicks
    track.addEventListener('touchmove', handleTouchMove, { passive: false }); // Non-passive for touchmove to allow preventDefault
    track.addEventListener('touchend', handleTouchEnd, { passive: false }); // Non-passive for touchend to allow preventDefault
    track.addEventListener('touchcancel', handleTouchCancel, { passive: true }); // Handle touch interruptions

    return () => {
      track.removeEventListener('touchstart', handleTouchStart);
      track.removeEventListener('touchmove', handleTouchMove);
      track.removeEventListener('touchend', handleTouchEnd);
      track.removeEventListener('touchcancel', handleTouchCancel);

      // Clear any pending timeout
      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
        touchTimeoutRef.current = null;
      }
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, handleTouchCancel]);

  return (
    <section className="portfolio" ref={portfolioRef}>
      <h2>Top Projects<br /></h2>
      <button className="discover-button" onClick={() => logDebug('Discover more clicked')}>DISCOVER MORE</button>
      <div
        className="portfolio-carousel"
        ref={carouselRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeaveCarousel}
      >
        <div
          className="carousel-track"
          ref={trackRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          {/* Render items twice for infinite scroll */}
          {[...PORTFOLIO_ITEMS, ...PORTFOLIO_ITEMS].map((item, index) => {
            // Calculate the original index to avoid duplicate tracking
            const originalIndex = index % PORTFOLIO_ITEMS.length;
            const uniqueKey = `${item.id || originalIndex}-${index >= PORTFOLIO_ITEMS.length ? 'duplicate' : 'original'}`;

            return (
              <div key={uniqueKey} className="portfolio-item">
                {item.forSale && (
                  <div className="for-sale-ribbon">
                    <span>Available</span>
                  </div>
                )}
                {item.href === "#" ? (
                  <button
                    onClick={() => {
                      trackProjectInteraction(item, 'unavailable-click', originalIndex);
                      setShowNotification(true);
                    }}
                    onMouseEnter={() => trackProjectInteraction(item, 'hover', originalIndex)}
                    style={{
                      background: 'none',
                      border: 'none',
                      padding: 0,
                      width: '100%',
                      cursor: 'pointer'
                    }}
                  >
                    <img src={item.image} alt={item.alt} />
                    <p>{item.title}</p>
                  </button>
                ) : (
                  <a
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => trackProjectInteraction(item, 'click', originalIndex)}
                    onMouseEnter={() => trackProjectInteraction(item, 'hover', originalIndex)}
                  >
                    <img src={item.image} alt={item.alt} />
                    <p>{item.title}</p>
                  </a>
                )}
              </div>
            );
          })}
          
          {/* Notification Modal */}
          {showNotification && (
            <div className="notification-overlay" onClick={() => setShowNotification(false)}>
              <div className="notification-message" onClick={(e) => e.stopPropagation()}>
                <h3>Coming Soon!</h3>
                <p>This exciting project is currently under development. We're crafting something extraordinary that combines innovation with exceptional user experience. Stay tuned for the unveiling!</p>
                <button className="notification-close" onClick={() => setShowNotification(false)}>
                  Got it!
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
});

Portfolio.displayName = 'Portfolio';

export default Portfolio;
